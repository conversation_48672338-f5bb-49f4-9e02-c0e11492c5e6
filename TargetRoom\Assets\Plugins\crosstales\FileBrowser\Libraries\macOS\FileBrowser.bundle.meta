fileFormatVersion: 2
guid: 467eddb584d74150bc204c7df46a4853
folderAsset: yes
PluginImporter:
  externalObjects: {}
  serializedVersion: 2
  iconMap: {}
  executionOrder: {}
  defineConstraints: []
  isPreloaded: 0
  isOverridable: 0
  isExplicitlyReferenced: 0
  validateReferences: 1
  platformData:
  - first:
      : Linux
    second:
      enabled: 1
      settings:
        CPU: x86
  - first:
      : LinuxUniversal
    second:
      enabled: 1
      settings:
        CPU: AnyCPU
  - first:
      : OSXIntel
    second:
      enabled: 1
      settings:
        CPU: AnyCPU
  - first:
      : OSXIntel64
    second:
      enabled: 1
      settings:
        CPU: AnyCPU
  - first:
      : SamsungTV
    second:
      enabled: 0
      settings:
        STV_MODEL: STANDARD_13
  - first:
      Android: Android
    second:
      enabled: 0
      settings:
        CPU: AnyCPU
  - first:
      Any: 
    second:
      enabled: 0
      settings: {}
  - first:
      Editor: Editor
    second:
      enabled: 0
      settings:
        CPU: AnyCPU
        DefaultValueInitialized: true
        OS: AnyOS
  - first:
      Standalone: Linux64
    second:
      enabled: 1
      settings:
        CPU: AnyCPU
  - first:
      Standalone: OSXUniversal
    second:
      enabled: 1
      settings:
        CPU: AnyCPU
  - first:
      Standalone: Win
    second:
      enabled: 1
      settings:
        CPU: AnyCPU
  - first:
      Standalone: Win64
    second:
      enabled: 1
      settings:
        CPU: AnyCPU
  - first:
      Windows Store Apps: WindowsStoreApps
    second:
      enabled: 0
      settings:
        CPU: AnyCPU
        DontProcess: False
        PlaceholderPath: 
        SDK: AnySDK
        ScriptingBackend: AnyScriptingBackend
  - first:
      iPhone: iOS
    second:
      enabled: 0
      settings:
        CompileFlags: 
        FrameworkDependencies: 
  - first:
      tvOS: tvOS
    second:
      enabled: 0
      settings:
        CompileFlags: 
        FrameworkDependencies: 
  userData: 
  assetBundleName: 
  assetBundleVariant: 
