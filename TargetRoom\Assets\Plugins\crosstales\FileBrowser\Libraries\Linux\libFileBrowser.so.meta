fileFormatVersion: 2
guid: 5346a1e3ecc4ef44a87035d71ab60a7b
PluginImporter:
  externalObjects: {}
  serializedVersion: 2
  iconMap: {}
  executionOrder: {}
  defineConstraints: []
  isPreloaded: 0
  isOverridable: 0
  isExplicitlyReferenced: 0
  validateReferences: 1
  platformData:
  - first:
      : Any
    second:
      enabled: 0
      settings:
        Exclude Android: 1
        Exclude Editor: 1
        Exclude Linux: 1
        Exclude Linux64: 0
        Exclude LinuxUniversal: 0
        Exclude OSXIntel: 1
        Exclude OSXIntel64: 1
        Exclude OSXUniversal: 1
        Exclude WebGL: 1
        Exclude Win: 0
        Exclude Win64: 0
        Exclude WindowsStoreApps: 1
        Exclude iOS: 1
        Exclude tvOS: 1
  - first:
      : Editor
    second:
      enabled: 0
      settings:
        CPU: AnyCPU
        OS: AnyOS
  - first:
      Android: Android
    second:
      enabled: 0
      settings:
        CPU: ARMv7
  - first:
      Any: 
    second:
      enabled: 0
      settings: {}
  - first:
      Editor: Editor
    second:
      enabled: 0
      settings:
        DefaultValueInitialized: true
  - first:
      Facebook: Win
    second:
      enabled: 0
      settings:
        CPU: AnyCPU
  - first:
      Facebook: Win64
    second:
      enabled: 0
      settings:
        CPU: AnyCPU
  - first:
      Standalone: Linux
    second:
      enabled: 0
      settings:
        CPU: None
  - first:
      Standalone: Linux64
    second:
      enabled: 1
      settings:
        CPU: AnyCPU
  - first:
      Standalone: LinuxUniversal
    second:
      enabled: 1
      settings:
        CPU: x86_64
  - first:
      Standalone: OSXIntel
    second:
      enabled: 0
      settings:
        CPU: None
  - first:
      Standalone: OSXIntel64
    second:
      enabled: 0
      settings:
        CPU: None
  - first:
      Standalone: OSXUniversal
    second:
      enabled: 0
      settings:
        CPU: None
  - first:
      Standalone: Win
    second:
      enabled: 1
      settings:
        CPU: AnyCPU
  - first:
      Standalone: Win64
    second:
      enabled: 1
      settings:
        CPU: AnyCPU
  - first:
      Windows Store Apps: WindowsStoreApps
    second:
      enabled: 0
      settings:
        CPU: AnyCPU
        DontProcess: False
        PlaceholderPath: 
        SDK: AnySDK
        ScriptingBackend: AnyScriptingBackend
  - first:
      iPhone: iOS
    second:
      enabled: 0
      settings:
        CompileFlags: 
        FrameworkDependencies: 
  - first:
      tvOS: tvOS
    second:
      enabled: 0
      settings:
        CompileFlags: 
        FrameworkDependencies: 
  userData: 
  assetBundleName: 
  assetBundleVariant: 
