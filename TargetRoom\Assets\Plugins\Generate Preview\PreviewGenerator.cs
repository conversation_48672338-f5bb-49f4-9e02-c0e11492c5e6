using UnityEngine;
using UnityEditor;

public class PreviewGenerator
{
    /// <summary>
    /// 生成预制体缩略图
    /// </summary>
    /// <param name="prefab">预制体对象</param>
    /// <param name="width">缩略图宽度</param>
    /// <param name="height">缩略图高度</param>
    /// <param name="savePath">保存路径</param>
    public static void GeneratePreview(GameObject prefab, int width, int height, string savePath)
    {
        // 创建预览相机
        var previewCamera = new GameObject("PreviewCamera").AddComponent<Camera>();
        previewCamera.clearFlags = CameraClearFlags.SolidColor;
        previewCamera.backgroundColor = Color.clear;
        previewCamera.orthographic = false;
        previewCamera.fieldOfView = 60f;
        previewCamera.enabled = false;

        try
        {
            // 实例化预制体
            var instance = GameObject.Instantiate(prefab);
            
            // 计算包围盒
            var bounds = CalculateBounds(instance);
            
            // 设置相机位置和参数
            SetupCamera(previewCamera, bounds);
            
            // 创建渲染纹理
            var rt = new RenderTexture(width, height, 24);
            previewCamera.targetTexture = rt;
            
            // 渲染
            previewCamera.Render();
            
            // 创建结果纹理
            var texture2D = new Texture2D(width, height, TextureFormat.RGBA32, false);
            RenderTexture.active = rt;
            texture2D.ReadPixels(new Rect(0, 0, width, height), 0, 0);
            texture2D.Apply();
            
            // 保存为PNG
            var bytes = texture2D.EncodeToPNG();
            System.IO.File.WriteAllBytes(savePath, bytes);
            
            // 清理
            GameObject.DestroyImmediate(instance);
            RenderTexture.active = null;
            rt.Release();
        }
        finally
        {
            // 清理相机
            GameObject.DestroyImmediate(previewCamera.gameObject);
        }
    }

    private static Bounds CalculateBounds(GameObject obj)
    {
        var bounds = new Bounds(obj.transform.position, Vector3.zero);
        var renderers = obj.GetComponentsInChildren<Renderer>();
        
        foreach (var renderer in renderers)
        {
            bounds.Encapsulate(renderer.bounds);
        }
        
        return bounds;
    }

    private static void SetupCamera(Camera camera, Bounds bounds)
    {
        // 计算45度角的相机位置
        float distance = bounds.size.magnitude * 1.2f;
        Vector3 offset = new Vector3(1f, 1f, -1f).normalized * distance;
        camera.transform.position = bounds.center + offset;
        camera.transform.LookAt(bounds.center);
    }
}