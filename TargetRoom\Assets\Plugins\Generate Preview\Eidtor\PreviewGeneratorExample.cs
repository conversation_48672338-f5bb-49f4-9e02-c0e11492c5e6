#if UNITY_EDITOR
using UnityEditor;
using UnityEngine;
public class PreviewGeneratorExample
{
    [MenuItem("Tools/Generate Preview")]
    public static void GeneratePreview()
    {
        // 获取选中的预制体
        var selectedObject = Selection.activeGameObject;
        if (selectedObject == null)
        {
            Debug.LogError("Please select a prefab first!");
            return;
        }

        // 生成预览图
        var savePath = $"Assets/AssetRaw/UIRaw/Atlas/SceneItemPreviews/{selectedObject.name}_preview.png";
        PreviewGenerator.GeneratePreview(selectedObject, 512, 512, savePath);
        
        // 刷新资源视图
        AssetDatabase.Refresh();
    }
}
#endif